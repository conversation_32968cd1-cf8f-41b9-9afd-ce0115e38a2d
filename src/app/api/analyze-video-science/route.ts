import { NextResponse } from 'next/server';
import { downloadVideoFromUrl, getVideoMetadata, identifyPlatform, isSupportedPlatform, validateVideoForGemini } from '@/lib/videoUrlProcessor';

export async function POST(request: Request) {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      return NextResponse.json({ message: 'Gemini API key is not configured.' }, { status: 500 });
    }

    const formData = await request.formData();
    const videoUrl = formData.get('videoUrl') as string;
    const videoFile = formData.get('video') as File;

    if (!videoUrl && !videoFile) {
      return NextResponse.json({ message: 'Either video URL or video file is required.' }, { status: 400 });
    }

    let videoAnalysis: any = null;

    if (videoFile) {
      // Handle uploaded video file
      const maxSize = 20 * 1024 * 1024; // 20MB limit for Gemini
      if (videoFile.size > maxSize) {
        return NextResponse.json({ message: 'Video file is too large. Maximum size is 20MB.' }, { status: 400 });
      }

      try {
        const arrayBuffer = await videoFile.arrayBuffer();
        const base64 = Buffer.from(arrayBuffer).toString('base64');

        const geminiResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent', {
          method: 'POST',
          headers: {
            'x-goog-api-key': GEMINI_API_KEY,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    inline_data: {
                      mime_type: videoFile.type,
                      data: base64
                    }
                  },
                  {
                    text: `Analyze this viral video to identify the behavioral science principles that make it engaging and shareable. Focus on psychological triggers and cognitive biases that capture attention and drive engagement.

Extract the following behavioral science principles with detailed explanations:

**BEHAVIORAL SCIENCE ANALYSIS:**
1. **Attention Triggers**: What immediately grabs attention (pattern interrupts, novelty, contrast)
2. **Cognitive Biases**: Which biases are leveraged (social proof, scarcity, authority, etc.)
3. **Emotional Hooks**: What emotions are triggered and how (curiosity, fear, joy, surprise)
4. **Psychological Principles**: Deeper psychological mechanisms at work (dopamine triggers, completion bias, etc.)
5. **Social Psychology**: How it leverages social dynamics (FOMO, tribal belonging, status)
6. **Neurological Triggers**: What activates the brain's reward systems

For each principle identified, provide:
- Clear name of the principle
- Detailed explanation of what it is and how it works psychologically
- Specific example of how this video uses that principle

Respond in JSON format:
{
  "videoAnalysis": {
    "fileName": "${videoFile.name}",
    "principles": [
      {
        "name": "Principle Name",
        "explanation": "Detailed explanation of the psychological principle and how it works",
        "videoExample": "Specific example of how this video demonstrates this principle"
      }
    ]
  }
}`
                  }
                ]
              }
            ]
          }),
        });

        if (!geminiResponse.ok) {
          const errorData = await geminiResponse.text();
          console.error('Gemini API error:', errorData);
          return NextResponse.json({ message: 'Failed to analyze video with Gemini API.' }, { status: 500 });
        }

        const geminiData = await geminiResponse.json();
        const analysisText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!analysisText) {
          return NextResponse.json({ message: 'No analysis received from Gemini API.' }, { status: 500 });
        }

        // Parse the JSON response from Gemini
        try {
          const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            videoAnalysis = JSON.parse(jsonMatch[0]);
          } else {
            console.error('No JSON found in Gemini response. Full response:', analysisText);
            throw new Error('No JSON found in response');
          }
        } catch (parseError) {
          console.error('Failed to parse Gemini response:', parseError);
          console.error('Raw response text:', analysisText);
          return NextResponse.json({ message: 'Failed to parse video analysis. The AI response was not in the expected format.' }, { status: 500 });
        }

      } catch (error) {
        console.error('Error processing video file:', error);
        return NextResponse.json({ message: 'Failed to process video file.' }, { status: 500 });
      }

    } else if (videoUrl) {
      // Handle video URL
      try {
        // Validate URL and platform
        if (!isSupportedPlatform(videoUrl)) {
          const platform = identifyPlatform(videoUrl);
          return NextResponse.json({
            message: `Unsupported platform: ${platform}. Supported platforms: TikTok, Instagram, YouTube, Twitter/X, Facebook.`
          }, { status: 400 });
        }

        // Get basic metadata first
        const metadata = await getVideoMetadata(videoUrl);
        if (!metadata.isValid) {
          return NextResponse.json({
            message: 'Invalid video URL. Please check the URL and try again.'
          }, { status: 400 });
        }

        // Attempt to download and process the video
        try {
          console.log(`Attempting to download video from ${metadata.platform}: ${videoUrl}`);
          const videoInfo = await downloadVideoFromUrl(videoUrl);

          // Validate the downloaded video
          const validation = validateVideoForGemini(videoInfo.buffer, videoInfo.mimeType);
          if (!validation.isValid) {
            return NextResponse.json({
              message: validation.error || 'Downloaded video is not compatible with analysis.'
            }, { status: 400 });
          }

          // Process the downloaded video with Gemini
          const base64 = videoInfo.buffer.toString('base64');
          console.log(`Video downloaded successfully, size: ${(videoInfo.buffer.length / 1024 / 1024).toFixed(1)}MB`);

          const geminiResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent', {
            method: 'POST',
            headers: {
              'x-goog-api-key': GEMINI_API_KEY,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contents: [
                {
                  parts: [
                    {
                      inline_data: {
                        mime_type: videoInfo.mimeType,
                        data: base64
                      }
                    },
                    {
                      text: `Analyze this viral video from ${videoInfo.platform} to identify the behavioral science principles that make it engaging and shareable. Focus on psychological triggers and cognitive biases that capture attention and drive engagement.

Extract the following behavioral science principles with detailed explanations:

**BEHAVIORAL SCIENCE ANALYSIS:**
1. **Attention Triggers**: What immediately grabs attention (pattern interrupts, novelty, contrast)
2. **Cognitive Biases**: Which biases are leveraged (social proof, scarcity, authority, etc.)
3. **Emotional Hooks**: What emotions are triggered and how (curiosity, fear, joy, surprise)
4. **Psychological Principles**: Deeper psychological mechanisms at work (dopamine triggers, completion bias, etc.)
5. **Social Psychology**: How it leverages social dynamics (FOMO, tribal belonging, status)
6. **Neurological Triggers**: What activates the brain's reward systems

For each principle identified, provide:
- Clear name of the principle
- Detailed explanation of what it is and how it works psychologically
- Specific example of how this video uses that principle

Respond in JSON format:
{
  "videoAnalysis": {
    "videoUrl": "${videoUrl}",
    "platform": "${videoInfo.platform}",
    "principles": [
      {
        "name": "Principle Name",
        "explanation": "Detailed explanation of the psychological principle and how it works",
        "videoExample": "Specific example of how this video demonstrates this principle"
      }
    ]
  }
}`
                    }
                  ]
                }
              ]
            }),
          });

          if (!geminiResponse.ok) {
            const errorData = await geminiResponse.text();
            console.error('Gemini API error:', errorData);
            return NextResponse.json({ message: 'Failed to analyze video with Gemini API.' }, { status: 500 });
          }

          const geminiData = await geminiResponse.json();
          const analysisText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

          if (!analysisText) {
            return NextResponse.json({ message: 'No analysis received from Gemini API.' }, { status: 500 });
          }

          // Parse the JSON response from Gemini
          try {
            const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              videoAnalysis = JSON.parse(jsonMatch[0]);
            } else {
              console.error('No JSON found in Gemini response. Full response:', analysisText);
              throw new Error('No JSON found in response');
            }
          } catch (parseError) {
            console.error('Failed to parse Gemini response:', parseError);
            console.error('Raw response text:', analysisText);
            return NextResponse.json({ message: 'Failed to parse video analysis. The AI response was not in the expected format.' }, { status: 500 });
          }

        } catch (downloadError) {
          // If video download fails, return a helpful error message
          const platform = identifyPlatform(videoUrl);
          console.error(`Video download error for ${platform}:`, downloadError);

          return NextResponse.json({
            message: `Unable to process video from ${platform}. ${downloadError instanceof Error ? downloadError.message : 'Unknown error occurred'}`,
            suggestion: 'Please try uploading the video file directly instead.',
            platform: platform
          }, { status: 400 });
        }

      } catch (error) {
        console.error('Error processing video URL:', error);
        return NextResponse.json({ message: 'Failed to process video URL.' }, { status: 500 });
      }
    }

    // Validate the videoAnalysis object before returning
    if (!videoAnalysis || typeof videoAnalysis !== 'object') {
      console.error('Invalid videoAnalysis object:', videoAnalysis);
      return NextResponse.json({
        message: 'Failed to generate valid video analysis.'
      }, { status: 500 });
    }

    // Ensure the principles array exists
    if (!videoAnalysis.principles || !Array.isArray(videoAnalysis.principles)) {
      console.warn('Missing principles array in videoAnalysis, initializing empty array');
      videoAnalysis.principles = [];
    }

    return NextResponse.json({ videoAnalysis });

  } catch (error) {
    console.error('Error in analyze-video-science API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
